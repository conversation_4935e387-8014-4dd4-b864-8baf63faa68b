<script setup>
import {definePage} from "unplugin-vue-router/runtime";
import {useAuthenticationStore} from '@/stores/authentication.js';
import {onMounted, watch} from "vue";
import {useRouter} from "vue-router";

const router = useRouter();
const authStore = useAuthenticationStore();

definePage({
  meta: {
    title: 'Welkom',
    requiresAuth: false,
    layout: 'public',
  },
})

// Redirect authenticated users to dashboard
function checkAuthAndRedirect() {
  if (authStore.isLoggedIn) {
    router.replace({ name: '/dashboard' });
  }
}
onMounted(() => {
  checkAuthAndRedirect();
});
watch(() => authStore.isLoggedIn, (isLoggedIn) => {
  if (isLoggedIn) {
    router.replace({ name: '/dashboard' });
  }
});
</script>

<template>
  <v-card>
    <v-card-title>
      Welkom bij Indufast Portal
    </v-card-title>
    <v-card-text>
      <p>Indufast Portal - Projectbeheer en tijdsregistratie systeem</p>
      <p>Deze pagina is beschikbaar voor verificatie doeleinden.</p>

      <v-btn
        color="primary"
        :to="{ path: '/login' }"
        class="mt-4"
      >
        Inloggen
      </v-btn>
    </v-card-text>
  </v-card>
</template>
