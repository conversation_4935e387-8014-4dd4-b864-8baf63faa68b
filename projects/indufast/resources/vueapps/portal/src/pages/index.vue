<script setup>
import {definePage} from "unplugin-vue-router/runtime";
import {useAuthenticationStore} from '@/stores/authentication.js';
import {onMounted, watch} from "vue";
import {useRouter} from "vue-router";

const router = useRouter();
const authStore = useAuthenticationStore();

definePage({
  meta: {
    title: 'Welkom',
    requiresAuth: false,
    layout: 'public',
  },
})
function checkAuthAndRedirect() {
  if (authStore.isLoggedIn) {
    router.replace({ name: '/dashboard' });
  }
}
onMounted(() => {
  checkAuthAndRedirect();
});
watch(() => authStore.isLoggedIn, (isLoggedIn) => {
  if (isLoggedIn) {
    router.replace({ name: '/dashboard' });
  }
});
</script>

<template>
  <div class="home-page">
    <v-card class="hero-section" elevation="0">
      <v-card-text class="text-center pa-8">
        <div class="mb-6">
          <img
            src="@/assets/logo_large.png"
            alt="Indufast"
            class="hero-logo mb-4"
          >
        </div>

        <h1 class="text-h3 mb-4 font-weight-bold">
          Welkom bij de Indufast Portal
        </h1>

        <p class="text-h6 mb-6 text-medium-emphasis">
          Uw centrale platform voor projectbeheer en tijdsregistratie
        </p>

        <v-btn
          color="primary"
          size="large"
          :to="{ path: '/login' }"
          class="px-8 py-3"
          elevation="2"
        >
          <v-icon left class="mr-2">mdi-login</v-icon>
          Inloggen
        </v-btn>
      </v-card-text>
    </v-card>

    <v-container class="py-8">
      <v-row justify="center">
        <v-col cols="12" md="10" lg="8">
          <h2 class="text-h4 text-center mb-8 font-weight-medium">
            Wat kunt u doen in de portal?
          </h2>

          <v-row>
            <v-col cols="12" sm="6" md="3">
              <v-card class="feature-card h-100" elevation="1">
                <v-card-text class="text-center pa-6">
                  <v-icon size="48" color="primary" class="mb-4">
                    mdi-clipboard-account-outline
                  </v-icon>
                  <h3 class="text-h6 mb-3">Planning</h3>
                  <p class="text-body-2">
                    Beheer en plan uw projecten en medewerkers efficiënt
                  </p>
                </v-card-text>
              </v-card>
            </v-col>

            <v-col cols="12" sm="6" md="3">
              <v-card class="feature-card h-100" elevation="1">
                <v-card-text class="text-center pa-6">
                  <v-icon size="48" color="primary" class="mb-4">
                    mdi-clipboard-check-multiple-outline
                  </v-icon>
                  <h3 class="text-h6 mb-3">Projecten</h3>
                  <p class="text-body-2">
                    Overzicht en beheer van al uw lopende projecten
                  </p>
                </v-card-text>
              </v-card>
            </v-col>

            <v-col cols="12" sm="6" md="3">
              <v-card class="feature-card h-100" elevation="1">
                <v-card-text class="text-center pa-6">
                  <v-icon size="48" color="primary" class="mb-4">
                    mdi-clock-check-outline
                  </v-icon>
                  <h3 class="text-h6 mb-3">Tijdsregistratie</h3>
                  <p class="text-body-2">
                    Registreer en beheer werkuren en projecttijd
                  </p>
                </v-card-text>
              </v-card>
            </v-col>

            <v-col cols="12" sm="6" md="3">
              <v-card class="feature-card h-100" elevation="1">
                <v-card-text class="text-center pa-6">
                  <v-icon size="48" color="primary" class="mb-4">
                    mdi-account-group-outline
                  </v-icon>
                  <h3 class="text-h6 mb-3">Medewerkers</h3>
                  <p class="text-body-2">
                    Beheer medewerkersgegevens en toegangsrechten
                  </p>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<style scoped>
.home-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

.hero-section {
  background: transparent;
  border-radius: 0;
}

.hero-logo {
  max-height: 120px;
  max-width: 300px;
  filter: drop-shadow(0 4px 8px rgba(0,0,0,0.1));
}

.feature-card {
  transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
  border-radius: 12px;
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.feature-card .v-icon {
  transition: transform 0.2s ease-in-out;
}

.feature-card:hover .v-icon {
  transform: scale(1.1);
}

@media (max-width: 600px) {
  .hero-logo {
    max-height: 80px;
    max-width: 200px;
  }

  .text-h3 {
    font-size: 1.8rem !important;
  }

  .text-h6 {
    font-size: 1.1rem !important;
  }
}
</style>
