<script setup>
import {definePage} from "unplugin-vue-router/runtime";
import {useAuthenticationStore} from '@/stores/authentication.js';
import {onMounted, ref, computed, watch} from "vue";
import {format} from "date-fns";
import createApiService from "@/services/api.js";
import {useRouter} from "vue-router";

const router = useRouter();
const api = createApiService(router);
const projects = ref([]);

const authStore = useAuthenticationStore();
const isAuthenticated = computed(() => authStore.isLoggedIn);

definePage({
  meta: {
    title: 'Welkom',
    requiresAuth: false,
    layout: 'public',
  },
})

onMounted(() => {
  if (isAuthenticated.value) {
    updateProjects();
  }
});

// Watch for authentication changes and update projects
watch(isAuthenticated, (newValue) => {
  if (newValue) {
    updateProjects();
  } else {
    projects.value = [];
  }
});

const updateProjects = () => {
  api
    .get("projectList?" + new URLSearchParams({
      archive: false,
      date: format(new Date(), 'yyyy-MM-dd'),
    }).toString())
    .then((response) => {
      projects.value = response.data.data;
    })
    .catch((error) => {
      console.log(error);
    });
}
</script>

<template>
  <v-card>
    <div v-if="isAuthenticated">
      <v-card-title>
        Welkom bij de Indufast Portal, {{ authStore.getCurrentUserName() }}
      </v-card-title>
      <v-card-text>
        <p>Het is vandaag <b>{{ $filters.formatDate(new Date()) }}</b>.</p>

        <!-- Navigation menu for authenticated users -->
        <v-container fluid class="pa-0 mt-4">
          <v-row dense>
            <v-col cols="12">
              <h3 class="mb-3">Navigatie</h3>
              <div class="d-flex flex-wrap ga-3">
                <v-btn
                  color="primary"
                  prepend-icon="mdi-clipboard-account-outline"
                  :to="{ name: '/planning' }"
                  variant="outlined"
                >
                  Planning
                </v-btn>
                <v-btn
                  color="primary"
                  prepend-icon="mdi-clipboard-check-multiple-outline"
                  :to="{ name: '/project/' }"
                  variant="outlined"
                >
                  Projecten
                </v-btn>
                <v-btn
                  v-if="authStore.hasPermission('edit')"
                  color="primary"
                  prepend-icon="mdi-clock-check-outline"
                  :to="{ name: '/tijdsregistratie/' }"
                  variant="outlined"
                >
                  Tijdsregistratie
                </v-btn>
                <v-btn
                  color="primary"
                  prepend-icon="mdi-account-group-outline"
                  :to="{ name: '/employees' }"
                  variant="outlined"
                >
                  Medewerkers
                </v-btn>
              </div>
            </v-col>
          </v-row>
        </v-container>

        <v-divider class="my-4" />

        <!-- Dashboard stats -->
        <v-container fluid class="pa-0">
          <v-row dense>
            <v-col cols="2">
              Aantal projecten
            </v-col>
            <v-col>
              {{ projects.length }}
            </v-col>
          </v-row>
          <v-row dense>
            <v-col cols="2">
              Laatste import Accredis-data
            </v-col>
            <v-col>
              @TODO
            </v-col>
          </v-row>
          <v-row dense>
            <v-col cols="2">
              Aantal te controleren werkdagen
            </v-col>
            <v-col>
              @TODO
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>
    </div>

    <!-- Public content for Google Cloud verification -->
    <div v-else>
      <v-card-title>
        Welkom bij Indufast Portal
      </v-card-title>
      <v-card-text>
        <p>Indufast Portal - Projectbeheer en tijdsregistratie systeem</p>
        <p>Deze pagina is beschikbaar voor verificatie doeleinden.</p>

        <v-btn
          color="primary"
          :to="{ path: '/login' }"
          class="mt-4"
        >
          Inloggen
        </v-btn>
      </v-card-text>
    </div>
  </v-card>
</template>
