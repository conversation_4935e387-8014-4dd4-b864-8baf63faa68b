<script setup>
import {definePage} from "unplugin-vue-router/runtime";
import {useAuthenticationStore} from '@/stores/authentication.js';
import {onMounted, watch} from "vue";
import {useRouter} from "vue-router";

const router = useRouter();
const authStore = useAuthenticationStore();

definePage({
  meta: {
    title: 'Welkom',
    requiresAuth: false,
    layout: 'public',
  },
})
function checkAuthAndRedirect() {
  if (authStore.isLoggedIn) {
    router.replace({ name: '/dashboard' });
  }
}
onMounted(() => {
  checkAuthAndRedirect();
});
watch(() => authStore.isLoggedIn, (isLoggedIn) => {
  if (isLoggedIn) {
    router.replace({ name: '/dashboard' });
  }
});
</script>

<template>
  <div class="home-page">
    <v-card class="hero-section" elevation="0">
      <v-card-text class="text-center pa-8">
        <div class="mb-6">
          <img
            src="@/assets/logo_large.png"
            alt="Indufast"
            class="hero-logo mb-4"
          >
        </div>

        <h1 class="text-h4 mb-4">
          Welkom bij Indufast Portal
        </h1>

        <p class="text-body-1 mb-6 text-medium-emphasis">
          Projectbeheer en tijdsregistratie
        </p>

        <v-btn
          color="primary"
          :to="{ path: '/login' }"
          class="px-6"
        >
          Inloggen
        </v-btn>
      </v-card-text>
    </v-card>

    <v-container class="py-6">
      <v-row justify="center">
        <v-col cols="12" md="8">
          <h2 class="text-h5 text-center mb-6">
            Functionaliteiten
          </h2>

          <v-row>
            <v-col cols="6" md="3">
              <div class="text-center pa-4">
                <v-icon size="32" color="primary" class="mb-2">
                  mdi-clipboard-account-outline
                </v-icon>
                <div class="text-body-2 font-weight-medium">Planning</div>
              </div>
            </v-col>

            <v-col cols="6" md="3">
              <div class="text-center pa-4">
                <v-icon size="32" color="primary" class="mb-2">
                  mdi-clipboard-check-multiple-outline
                </v-icon>
                <div class="text-body-2 font-weight-medium">Projecten</div>
              </div>
            </v-col>

            <v-col cols="6" md="3">
              <div class="text-center pa-4">
                <v-icon size="32" color="primary" class="mb-2">
                  mdi-clock-check-outline
                </v-icon>
                <div class="text-body-2 font-weight-medium">Tijdsregistratie</div>
              </div>
            </v-col>

            <v-col cols="6" md="3">
              <div class="text-center pa-4">
                <v-icon size="32" color="primary" class="mb-2">
                  mdi-account-group-outline
                </v-icon>
                <div class="text-body-2 font-weight-medium">Medewerkers</div>
              </div>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<style scoped>
.home-page {
  min-height: 100vh;
  background-color: #fafafa;
}

.hero-section {
  background: transparent;
}

.hero-logo {
  max-height: 100px;
  max-width: 250px;
}

@media (max-width: 600px) {
  .hero-logo {
    max-height: 80px;
    max-width: 200px;
  }
}
</style>
