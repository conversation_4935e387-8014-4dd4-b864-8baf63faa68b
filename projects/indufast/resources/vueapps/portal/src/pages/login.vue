<script setup>
import { onMounted, ref } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useAuthenticationStore } from '@/stores/authentication.js';
import {useConfigStore} from "@/stores/config.js";
import { definePage } from "unplugin-vue-router/runtime";

import createApiService from '@/services/api';

const router = useRouter();
const route = useRoute();
const authStore = useAuthenticationStore();
const configStore = useConfigStore();
const api = createApiService(router);

definePage({
  meta: {
    title: 'Inloggen',
    requiresAuth: false,
    layout: 'unauthenticated',
  },
})

const loading = ref(false);
const email = ref('');
const password = ref('');
const loginErrors = ref([]);
const initialLoadComplete = ref(false);

const submitLogin = async () => {
  loading.value = true;
  resetErrors();

  try {
    const response = await api.post('login', {email: email.value, password: password.value});
    if (response.status === 200) {
      const authStore = useAuthenticationStore();
      authStore.setLoginData(response.data.data.user, true);
      configStore.setConfig(response.data.data.config);
      authStore.clearToken();

      await router.push('/');
    }
    else {
      loginErrors.value = ['Er is iets misgegaan. Probeer het later nog eens.'];
    }
  } catch (e) {
    loginErrors.value = e.response?.status === 400 ? e.response.data.message : ['Er is iets misgegaan. Probeer het later nog eens.'];
  } finally {
    loading.value = false;
  }
}

const handleGoogleLogin = async () => {
  loading.value = true;
  resetErrors();

  try {
    // Redirect to Google OAuth endpoint
    const response = await api.get('googleAuthUrl');
    if (response.status === 200 && response.data.data.url) {
      window.location.href = response.data.data.url;
    } else {
      loginErrors.value = ['Er is iets misgegaan. Probeer het later nog eens.'];
    }
  } catch (e) {
    loginErrors.value = e.response?.status === 400 ? e.response.data.message : ['Er is iets misgegaan. Probeer het later nog eens.'];
  } finally {
    loading.value = false;
  }
}

const handleGoogleLoginCallback = async () => {
  const success = route.query.google_login_success;
  const user = route.query.user;
  const token = route.query.token;
  const error = route.query.google_login_error;
  const config = route.query.config;

  if (success && user) {
    try {
      authStore.setLoginData(JSON.parse(decodeURIComponent(user)), true);
      authStore.setToken(JSON.parse(decodeURIComponent(token)));
      configStore.setConfig(JSON.parse(decodeURIComponent(config)));
      await router.push('/');
      return true;
    } catch (e) {
      console.error('Error parsing user data from Google login:', e);
      loginErrors.value = ['Er is iets misgegaan. Probeer het later nog eens.'];
    }
  } else if (error) {
    loginErrors.value = [decodeURIComponent(error)];
  }

  const currentPath = router.currentRoute.value.path;
  await router.replace({path: currentPath});
  return false;
}

const resetErrors = () => {
  loginErrors.value = [];
}

onMounted(async () => {
  const redirected = await handleGoogleLoginCallback();
  if (!redirected) {
    initialLoadComplete.value = true;
  }
});
</script>

<template>
  <v-card
    class="ma-auto mt-9 pa-5"
    max-width="500"
    :loading="loading"
    :disabled="loading"
    elevation="10"
  >
    <img
      src="@/assets/logo_large.png"
      class="logo-large"
      alt="Indufast"
    >

    <v-form
      v-if="initialLoadComplete"
      @submit.prevent="submitLogin"
    >
      <v-card-text class="pl-0 pr-0">
        <v-alert
          v-if="loginErrors.length"
          class="mb-5"
          type="error"
          variant="tonal"
        >
          <template #text>
            <span
              v-for="(error, index) in loginErrors"
              :key="index"
            >
              {{ error }}
            </span>
          </template>
        </v-alert>
        <v-text-field
          v-model="email"
          class="mb-2"
          label="E-mail"
          hide-details
        />
        <v-text-field
          v-model="password"
          type="password"
          label="Wachtwoord"
          hide-details
        />
      </v-card-text>

      <v-card-actions class="flex-column ma-auto pa-0 mt-2 actions">
        <v-btn
          class="mb-2"
          text="Inloggen"
          variant="elevated"
          type="submit"
          color="primary"
          block
        />
        <v-btn
          prepend-icon="mdi-google"
          text="Inloggen met Google"
          variant="elevated"
          color="error"
          block
          @click="handleGoogleLogin"
        />
      </v-card-actions>
    </v-form>

    <div
      v-else
      class="text-center py-10"
    >
      <v-progress-circular
        indeterminate
        color="primary"
      />
    </div>
  </v-card>
</template>

<style scoped>
.logo-large {
  max-width: 100%;
}
.actions {
  min-height: auto;
}
</style>
