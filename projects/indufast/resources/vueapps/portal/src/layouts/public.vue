<template>
  <v-main class="d-flex flex-column flex-grow-1">
    <v-container
      fluid
      class="flex-grow-1 d-flex flex-column pb-0"
    >
      <v-app-bar
        scroll-behavior="elevate"
        color="GSDBackground"
        density="compact"
        class="pt-2 mb-4"
      >
        <router-link :to="{name: '/'}">
          <img
            src="@/assets/logo.png"
            class="logo ml-4"
            alt="Indufast"
          >
        </router-link>
        <v-spacer />

        <v-btn
          color="primary"
          :to="{ path: '/login' }"
          class="mr-4"
        >
          Inloggen
        </v-btn>
      </v-app-bar>

      <v-card
        border
        class="flex-grow-1 d-flex flex-column"
      >
        <router-view />
      </v-card>
    </v-container>
    <the-footer />
  </v-main>
</template>

<script setup>
import TheFooter from "@/components/TheFooter.vue";
</script>

<style lang="scss">
.logo {
  max-height: 40px;
  max-width: 200px;
}
</style>
