<?php

  namespace domain\google\service;

  use Config;
  use DateTimeInterface;
  use Google\Client;
  use Google\Service\Calendar;
  use Google\Service\Calendar\Event;
  use Google\Service\Calendar\EventAttendee;
  use Google\Service\Calendar\EventDateTime;
  use Google\Service\Calendar\EventReminders;
  use Google\Service\Exception;
  use IndufastCalendarEvent;
  use IndufastGoogleDriveFile;
  use IndufastProject;
  use Psr\Http\Message\RequestInterface;

  class GoogleCalendarService {

    protected Calendar $calendarService;

    /**
     * @throws \Exception
     */
    public function __construct() {
      $client = new Client();
      $client->setAuthConfig(Config::get('GOOGLE_API_AUTH_CONFIG'));
      $client->setSubject(Config::get('GOOGLE_API_SUBJECT'));
      $client->setAccessType('offline');
      $client->addScope(Calendar::CALENDAR);

      $this->calendarService = new Calendar($client);
    }

    /**
     * @throws \Exception
     */
    public function updateGoogleCalendarEvent(\IndufastCalendarEvent $event): void {
      $project = $event->project();

      $start = new EventDateTime();
      $start->setDateTime((new \DateTime($event->start))->format(DateTimeInterface::RFC3339));

      $end = new EventDateTime();
      $end->setDateTime((new \DateTime($event->end))->format(DateTimeInterface::RFC3339));

      $googleEvent = new Event();
      if ($event->google_calendar_event_id) {
        try {
          $googleEvent = $this->calendarService->events->get(Config::get('GOOGLE_API_CALENDAR_ID'), $event->google_calendar_event_id);
        } catch (Exception $e) {
          // If the event does not exist, suppress the error and create it below.
          if ($e->getCode() !== 404) {
            throw $e;
          }
        }
      }

      $googleEvent->setGuestsCanInviteOthers(false);
      $googleEvent->setReminders(new EventReminders(['useDefault' => true]));
      $googleEvent->setStart($start);
      $googleEvent->setEnd($end);
      $googleEvent->setLocation($project->address);
      $googleEvent->setStatus('confirmed');

      if ($event->type === IndufastCalendarEvent::TYPE_BLAST) {
        $googleEvent->setSummary('Straaldag: ' . $project->name . ($event->confirmed ? '' : ' (niet bevestigd)'));
        $googleEvent->setColorId(IndufastProject::COLOR_ID_BANANA);
        $googleEvent->setDescription($event->remark);
      }
      else {
        $googleEvent->setSummary($project->name);
        $googleEvent->setColorId($project->getColorId());
        $googleEvent->setDescription($project->getCalenderDescription($event));
      }

      $attendees = [];
      foreach ($event->employees() as $eventEmployee) {
        $attendee = new EventAttendee();
        $attendee->setEmail($eventEmployee->employee->email);
        $attendees[] = $attendee;
      }
      $googleEvent->setAttendees($attendees);

      if ($event->type === IndufastCalendarEvent::TYPE_WORK) {
        $files = IndufastGoogleDriveFile::find_all_by(['project_id' => $project->id]);
        $attachments = [];
        foreach ($files as $file) {
          $attachments[] = [
            'fileId'   => $file->id,
            'fileUrl'  => $file->url,
            'title'    => $file->name,
            'iconLink' => $file->icon_url,
          ];
        }
        $googleEvent->setAttachments($attachments);
      }

      if ($googleEvent->id) {
        $this->calendarService->events->update(
          Config::get('GOOGLE_API_CALENDAR_ID'),
          $event->google_calendar_event_id,
          $googleEvent,
          ['supportsAttachments' => true]
        );
      }
      else {
        $newEvent = $this->calendarService->events->insert(Config::get('GOOGLE_API_CALENDAR_ID'), $googleEvent);
        $event->google_calendar_event_id = $newEvent->getId();
      }
    }

    /**
     * @throws \Exception
     */
    public function deleteGoogleCalendarEvent(\IndufastCalendarEvent $event): void {
      try {
        $this->calendarService->events->delete(Config::get('GOOGLE_API_CALENDAR_ID'), $event->google_calendar_event_id);
      }
      catch (\Exception $e) {
        // Event no longer exists, ignore.
        if ($e->getCode() !== 410) {
          throw $e;
        }
      }
      $event->google_calendar_event_id = null;
    }

    public function getGoogleCalendarEventsBatch(array $calendarIds, string $dateStart, ?string $dateEnd = null): array
    {
      $start = new \DateTime($dateStart . ' 00:00:00');
      $end = new \DateTime(($dateEnd ?? $dateStart) . ' 23:59:59');

      $client = $this->calendarService->getClient();
      $client->setUseBatch(true);

      $batch =  $this->calendarService->createBatch();

      $optParams = [
        'timeMin'               => $start->format(DateTimeInterface::RFC3339),
        'timeMax'               => $end->format(DateTimeInterface::RFC3339),
        'timeZone'              => 'Europe/Amsterdam',
        'showHiddenInvitations' => true,
        'singleEvents'          => true,
        'fields'                => 'items/id,items/start,items/end,items/summary,items/description,items/location,items/visibility,items/transparency,items/colorId,items/attendees,items/status',
      ];

      foreach ($calendarIds as $calendarId) {
        /** @var RequestInterface $request */
        $request = $this->calendarService->events->listEvents($calendarId, $optParams);
        $batch->add($request, $calendarId);
      }

      $results = $batch->execute();
      $client->setUseBatch(false);

      // Group results by calendar ID
      $eventsByCalendar = [];
      foreach ($results as $key => $events) {
        // The key is 'response-<calendar-id>' so we remove the prefix
        $calendarId = str_starts_with($key, 'response-') ? substr($key, strlen('response-')) : $key;
        $eventsByCalendar[$calendarId] = $events;
      }
      return $eventsByCalendar;
    }

    public function getGoogleEventFiles(\IndufastCalendarEvent $indufastEvent): array {
      try {
        $event = $this->calendarService->events->get(Config::get('GOOGLE_API_CALENDAR_ID'), $indufastEvent->google_calendar_event_id, ['fields' => 'attachments']);
        return $event->getAttachments() ?? [];
      } catch (Exception $e) {
        if ($e->getCode() !== 404) {
          throw $e;
        }
      }
      return [];
    }
  }
