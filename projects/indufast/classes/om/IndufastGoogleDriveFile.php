<?php

AppModel::loadModelClass('IndufastGoogleDriveFileModel');

class IndufastGoogleDriveFile extends IndufastGoogleDriveFileModel {
  use ModelFillTrait;
  use ValidationTrait;
  use PropertyCastTrait {
    castProperties as castPropertiesTrait;
  }

  const CAST_PROPERTIES = [
    'id'                => 'int',
    'project_id'        => 'int',
    'from_db'           => 'hidden',
  ];

  protected array $fillable = [
    'name'        => 'string|required|max:255',
    'url'         => 'string|required|max:255',
    'icon_url'    => 'string|required|max:255',
    'project_id'  => 'int|required|exists:indufast_project,id',
  ];
}