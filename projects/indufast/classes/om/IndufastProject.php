<?php

  use domain\google\service\GoogleCalendarService;

  AppModel::loadModelClass('IndufastProjectModel');

  class IndufastProject extends IndufastProjectModel {

    use PropertyCastTrait {
      castProperties as castPropertiesTrait;
    }
    use ModelFillTrait;
    use ValidationTrait;

    /** @var IndufastCalendarEvent[] */
    public array $events = [];
    public ?string $start = null;
    public ?string $end = null;
    public ?string $plannable = null;
    public array $google_drive_files = [];
    public array $default_relations = ['events', 'plannable'];

    /**
     * @deprecated Use event type instead.
     */
    const STATUS_BLAST_SAND = 'blast_sand';
    const STATUS_PLANNING = 'planning';
    const STATUS_INCOMPLETE = 'incomplete';
    const STATUS_WEATHER_DEPENDENT = 'weather_dependent';
    const STATUS_COMPLETE = 'complete';
    const STATUS_UNPLANNED = 'unplanned';

    const MATERIAL_LOAD_BLADEL = 'bladel';
    const MATERIAL_LOAD_ALBLASSERDAM = 'alblasserdam';
    const PLANNABLE = 'plannable';
    const NOT_PLANNABLE_INTERNAL = 'not_plannable_internal';
    const NOT_PLANNABLE_EXTERNAL = 'not_plannable_external';

    const int COLOR_ID_SAGE = 2;
    const int COLOR_ID_BANANA = 5;
    const int COLOR_ID_TANGERINE = 6;
    const int COLOR_ID_PEACOCK = 7;
    const int COLOR_ID_TOMATO = 11;

    const CAST_PROPERTIES = [
      'id'                        => 'int',
      'fte'                       => 'int',
      'void'                      => 'boolean',
      'from_db'                   => 'hidden',
      'insertTS'                  => 'hidden',
      'updateTS'                  => 'hidden',
    ];

    protected array $fillable = [
      'name'                      => 'required|string|max:255',
      'remark'                    => 'nullable|string',
      'fte'                       => 'nullable|integer|min:1|max:100',
      'project_number'            => 'required|string|max:32|unique:indufast_project,project_number,id,{id}',
      'project_number_exact'      => 'required|string|max:32|unique:indufast_project,project_number_exact,id,{id}',
      'customer_name'             => 'required|string|max:255',
      'contact_name'              => 'nullable|string|max:255',
      'contact_email'             => 'nullable|email|max:255',
      'contact_number'            => 'nullable|string|max:32',
      'address'                   => 'required|string|max:255',
      'google_drive_files'        => 'array',
      'void'                      => 'boolean',
    ];

    public function getFillable(): array {
      $this->fillable['status'] = 'string|required|in:' . implode(',', [
        self::STATUS_BLAST_SAND,
        self::STATUS_PLANNING,
        self::STATUS_INCOMPLETE,
        self::STATUS_WEATHER_DEPENDENT,
        self::STATUS_COMPLETE,
        self::STATUS_UNPLANNED,
      ]);

      $this->fillable['material_load'] = 'string|nullable|in:' . implode(',', [
        self::MATERIAL_LOAD_BLADEL,
        self::MATERIAL_LOAD_ALBLASSERDAM,
      ]);

      return $this->fillable;
    }

    public function getColorId(): int {
      return match ($this->status) {
        self::STATUS_BLAST_SAND => self::COLOR_ID_BANANA,
        self::STATUS_PLANNING => self::COLOR_ID_PEACOCK,
        self::STATUS_INCOMPLETE => self::COLOR_ID_TOMATO,
        self::STATUS_WEATHER_DEPENDENT => self::COLOR_ID_TANGERINE,
        self::STATUS_COMPLETE => self::COLOR_ID_SAGE,
        default => 0,
      };
    }

    public function castProperties(): void {
      $this->castPropertiesTrait();
      $this->plannable();

      $first = null;
      $last = null;
      foreach ($this->events() as $event) {
        $first = min($first ?? $event->start, $event->start);
        $last = max($last, $event->end);

        $event->castProperties();
      }

      $this->start = $first;
      $this->end = $last;
      $this->event_count = $this->events ? count($this->events) : null;
    }

    public function save(array &$errors = []): mysqli_result|bool {
      $this->castPropertiesForSave();
      $result = parent::save($errors);
      $this->saveGoogleDriveFiles();
      return $result;
    }

    private function saveGoogleDriveFiles(): void {
      if (!isset($this->google_drive_files)) return;

      // Cast to array of objects if not already
      $this->google_drive_files = array_map(fn($file) => is_object($file) ? $file : (object)$file, $this->google_drive_files);

      $existingFiles = IndufastGoogleDriveFile::find_all_by(['project_id' => $this->id]);
      $existingIds = array_column($existingFiles, 'id');
      $submittedIds = array_column($this->google_drive_files, 'id');

      // Add new files (submitted files without ID or with ID not in existing)
      $filesToAdd = array_filter($this->google_drive_files, fn($file) => empty($file->id) || !in_array($file->id, $existingIds));

      foreach ($filesToAdd as $file) {
        $fileToSave = new IndufastGoogleDriveFile();
        $fileToSave->fill([
            'name' => $file->name,
            'url' => $file->url,
            'icon_url' => $file->icon_url,
            'project_id' => $this->id,
        ]);
        $fileToSave->castPropertiesForSave();
        $fileToSave->save();
      }

      // Delete files that are no longer submitted
      $filesToDelete = array_filter($existingFiles, fn($existingFile) => !in_array($existingFile->id, $submittedIds));

      foreach ($filesToDelete as $file) {
        $file->destroy();
      }
  }

    /**
     * Format the project data for the Google Calendar event description.
     * @throws DateMalformedStringException
     */
    public function getCalenderDescription(IndufastCalendarEvent $event): string {
      $remark = str_replace(["\n", "\r"], '', $this->remark ?? '');
      $remark = preg_replace('/(<p>(?:&nbsp;|\s)*<\/p>)*$/', '', $remark);

      $load = '';
      if ($this->material_load) {
        $load = ($event->materialLoaders()) ? implode(', ', array_column($event->material_loaders, 'name')) . ' (' . ucfirst($this->material_load) . ')': ucfirst($this->material_load);
      }

      $input = [
        'Extra opmerking'           => escapeSafe($event->remark),
        'Klantnaam'                 => escapeSafe($this->customer_name),
        'Contactpersoon'            => escapeSafe(implode(", ", array_filter([
          $this->contact_name,
          $this->contact_number,
          $this->contact_email,
        ]))),
        'Projectnummer'             => escapeSafe($this->project_number),
        'Aanvangstijd'              => new DateTime($event->start)->format('H:i') . ' - Minimaal 15 minuten voor aanvang werkzaamheden aanwezig zijn.',
        'Materiaal laden'           => $load,
        'Contactpersoon bouwplaats' => ($event->teamLead()) ? $event->team_lead->name : '',
        'Omschrijving'              => $remark,
        'Let op'                    => '<ul><li>Afval altijd netjes gestapeld achterlaten als er geen container is.</li><li>Vloer nameten en dit doorgeven per <NAME_EMAIL>.</li><li>Verbruik van mortel, thix, gel altijd noteren en doorgeven.</li></ul>',
      ];

      $output = [];
      foreach ($input as $title => $content) {
        if ($content) {
          $output[] = '<p><b><u>' . escapeSafe($title) . '</u>:</b> ' . $content . '</p>';
        }
      }

      return strip_tags(implode("", $output), ['p', 'b', 'br', 'ul', 'ol', 'li', 'i', 'em', 'u', 'strong']);
    }

    public function getGoogleFiles(): array {
      $indufastEvent = \IndufastCalendarEvent::find_by(['project_id' => $this->id, 'type' => 'work']);

      // If no work event are Google event is found, return database files.
      $databaseFiles = IndufastGoogleDriveFile::find_all_by(['project_id' => $this->id]);
      if (!$indufastEvent) return $databaseFiles;

      $files = new GoogleCalendarService()->getGoogleEventFiles($indufastEvent);
      if (empty($files)) return $databaseFiles;

      return array_map(fn($file) => (object)[
        'id' => $file['fileId'],
        'name' => $file['title'],
        'url' => $file['fileUrl'],
        'icon_url' => $file['iconLink'],
      ], $files);
    }

    public function plannable(): string
    {
      foreach ($this->events() as $event) {
        foreach ($event->employees() as $employee) {
          if ($employee->conflict) {
            $conflict = is_string($employee->conflict) ? json_decode($employee->conflict) : $employee->conflict;
            if ($conflict && isset($conflict->internal)) {
              if ($conflict->internal) {
                return $this->plannable = self::NOT_PLANNABLE_INTERNAL;
              }
              return $this->plannable = self::NOT_PLANNABLE_EXTERNAL;
            }
          }
        }
      }
      return $this->plannable = self::PLANNABLE;
    }

    /**
     * @return IndufastCalendarEvent[]
     */
    public function events(): array {
      return $this->events = IndufastCalendarEvent::find_all_by(['project_id' => $this->id], 'ORDER BY start');
    }

    /**
     * @return IndufastGoogleDriveFile[]
     */
    public function google_drive_files(): array {
      return $this->google_drive_files = IndufastGoogleDriveFile::find_all_by(['project_id' => $this->id], 'ORDER BY name');
    }
  }