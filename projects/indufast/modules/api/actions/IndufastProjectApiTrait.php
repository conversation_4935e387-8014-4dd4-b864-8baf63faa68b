<?php

  use classes\ApiResponse;

  trait IndufastProjectApiTrait {

    public function executeProjectCreate(): void {
      $project = new IndufastProject();
      $project->fill($this->data)->validateFillable();

      try {
        $project->save();

        ApiResponse::sendResponseOK('Project saved', $project);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while saving.');
      }
    }

    public function executeProjectList(): void {
      $rules = [
        'date'    => 'prohibited_unless:year,|date:Y-m-d',
        'archive' => 'string|in:true,false',
        'year' => 'required_with:month|date:Y',
        'month' => 'date:m',
        'employee_id' => 'integer|exists:indufast_employee,id',
      ];
      $validation = $this->validateData($_GET, $rules);
      $data = $validation->getValidatedData();

      $findBy = [
        'void' => isset($data['archive']) && $data['archive'] == 'true'
      ];

      if ($data['date']) {
        // @TODO: does not work with multiday events.
        $events = IndufastCalendarEvent::find_all(sprintf("WHERE start >= '%s' AND end <= '%s'",
          $data['date'] . ' 00:00:00',
          $data['date'] . ' 23:59:59',
        ));
        $findBy['id'] = array_unique(array_column($events, 'project_id'));
      }

      if ($data['year']) {
        $filters[] = sprintf("YEAR(start) = %d", $data['year']);

        $filters = [];
        if ($data['month']) {
          $filters[] = sprintf("MONTH(start) = %d", $data['month']);
        }

        $joins = [];
        if ($data['employee_id']) {
          $joins[] = sprintf(
            'JOIN indufast_calendar_event_employee AS ice ON ice.calendar_event_id = indufast_calendar_event.id AND ice.employee_id = %d',
            (int)$data['employee_id'],
          );
        }

        $events = IndufastCalendarEvent::find_all(implode(' ', [
          implode(' ', $joins),
          ($filters) ? 'WHERE ' . implode(' AND ', $filters) : '',
          'ORDER BY start DESC',
        ]));

        $findBy['id'] = array_unique(array_column($events, 'project_id'));
      }

      $projects = IndufastProject::find_all_by($findBy, 'ORDER BY id DESC');
      ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $projects);
    }

    public function executeProjectUpdate(): void {
      if (!$project = IndufastProject::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Project not found');
      }

      $project->fill($this->data)->validateFillable();

      try {
        $project->save();
        ApiResponse::sendResponseOK('Project saved', $project);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while saving.', (DEVELOPMENT) ? $e->getMessage() : '');
      }
    }

    public function executeProjectDelete(): void {
      if (!$project = IndufastProject::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Project not found');
      }

      try {
        $project->void = true;
        $project->save();

        ApiResponse::sendResponseOK('Project deleted', $project);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while deleting.');
      }
    }

    public function executeProjectGetGoogleFiles(): void {
      if (!$project = IndufastProject::find_by_id($_GET['id'] ?? null)) {
        ApiResponse::sendResponseNotFound('Project not found');
      }

      try {
        $files = $project->getGoogleFiles();
        ApiResponse::sendResponseOK(ApiResponse::MESSAGE_OK, $files);
      }
      catch (\Exception $e) {
        logToFile(__CLASS__, var_export($e->getMessage(), true));
        ApiResponse::sendResponseError('Unknown error occured while retrieving files.');
      }
    }
  }